"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const electron_store_1 = __importDefault(require("electron-store"));
const index_1 = require("../../bot/src/index");
// Initialize electron store for persistent settings
const store = new electron_store_1.default({
    defaults: {
        windowBounds: { width: 1200, height: 800 },
        tradingSettings: {
            threshold: 0.02,
            tradeAmount: 1,
            autoTrade: false,
            maxTrades: 10
        }
    }
});
let mainWindow = null;
let tradingBot = null;
const createWindow = () => {
    // Get stored window bounds or use defaults
    const windowBounds = store.get('windowBounds');
    // Create the browser window
    mainWindow = new electron_1.BrowserWindow({
        width: windowBounds.width,
        height: windowBounds.height,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, '../assets/icon.png'), // We'll add this later
        title: 'Pocket Option Trading Bot',
        minWidth: 800,
        minHeight: 600,
        show: false // Don't show until ready
    });
    // Load the renderer app
    const isDev = process.env.NODE_ENV === 'development';
    if (isDev) {
        mainWindow.loadURL('http://localhost:5173');
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile(path.join(__dirname, '../renderer/dist/index.html'));
    }
    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow?.show();
    });
    // Save window bounds on resize/move
    mainWindow.on('resize', () => {
        if (mainWindow) {
            store.set('windowBounds', mainWindow.getBounds());
        }
    });
    mainWindow.on('move', () => {
        if (mainWindow) {
            store.set('windowBounds', mainWindow.getBounds());
        }
    });
    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
};
// App event handlers
electron_1.app.whenReady().then(() => {
    createWindow();
    electron_1.app.on('activate', () => {
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
// IPC handlers for communication with renderer
electron_1.ipcMain.handle('get-settings', () => {
    return store.get('tradingSettings');
});
electron_1.ipcMain.handle('save-settings', (_, settings) => {
    store.set('tradingSettings', settings);
    return true;
});
electron_1.ipcMain.handle('show-error-dialog', (_, title, content) => {
    if (mainWindow) {
        electron_1.dialog.showErrorBox(title, content);
    }
});
electron_1.ipcMain.handle('show-info-dialog', async (_, title, content) => {
    if (mainWindow) {
        const result = await electron_1.dialog.showMessageBox(mainWindow, {
            type: 'info',
            title,
            message: content,
            buttons: ['OK']
        });
        return result;
    }
});
// Bot control IPC handlers
electron_1.ipcMain.handle('start-bot', async (_, settings) => {
    try {
        if (tradingBot && tradingBot.getStatus().isRunning) {
            return { success: false, message: 'Bot is already running' };
        }
        // Create new bot instance
        tradingBot = (0, index_1.createBot)(settings, {
            headless: false, // Set to true for production
            userDataDir: path.join(electron_1.app.getPath('userData'), 'browser-session')
        });
        // Set up event listeners
        tradingBot.on('status-update', status => {
            mainWindow?.webContents.send('bot-status-update', status);
        });
        tradingBot.on('price-update', priceData => {
            mainWindow?.webContents.send('price-update', priceData);
        });
        tradingBot.on('trade-result', result => {
            mainWindow?.webContents.send('trade-result', result);
        });
        tradingBot.on('log', logEntry => {
            console.log(`[BOT ${logEntry.level.toUpperCase()}] ${logEntry.message}`);
        });
        tradingBot.on('error', error => {
            console.error('[BOT ERROR]', error);
            mainWindow?.webContents.send('bot-error', error);
        });
        // Start the bot
        await tradingBot.start();
        return { success: true, message: 'Bot started successfully' };
    }
    catch (error) {
        console.error('Failed to start bot:', error);
        return { success: false, message: `Failed to start bot: ${error}` };
    }
});
electron_1.ipcMain.handle('stop-bot', async () => {
    try {
        if (!tradingBot) {
            return { success: false, message: 'No bot instance found' };
        }
        await tradingBot.stop();
        tradingBot.removeAllListeners();
        tradingBot = null;
        return { success: true, message: 'Bot stopped successfully' };
    }
    catch (error) {
        console.error('Failed to stop bot:', error);
        return { success: false, message: `Failed to stop bot: ${error}` };
    }
});
electron_1.ipcMain.handle('get-bot-status', () => {
    if (!tradingBot) {
        return {
            isRunning: false,
            currentPrice: 0,
            tradesCount: 0,
            winCount: 0,
            lossCount: 0,
            winRate: 0,
            totalProfit: 0,
            lastTrade: null
        };
    }
    return tradingBot.getStatus();
});
electron_1.ipcMain.handle('update-bot-settings', (_, settings) => {
    if (tradingBot) {
        tradingBot.updateSettings(settings);
        return { success: true, message: 'Settings updated successfully' };
    }
    return { success: false, message: 'No bot instance found' };
});
//# sourceMappingURL=main.js.map