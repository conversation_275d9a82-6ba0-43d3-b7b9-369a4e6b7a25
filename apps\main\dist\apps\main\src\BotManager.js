"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BotManager = void 0;
const path = __importStar(require("path"));
const electron_1 = require("electron");
// Import the bot dynamically to avoid compilation issues
let TradingBot = null;
let createBot = null;
// Dynamically import the bot
async function loadBot() {
    if (!TradingBot) {
        try {
            const botModule = await Promise.resolve().then(() => __importStar(require('../../bot/dist/apps/bot/src/index.js')));
            TradingBot = botModule.TradingBot;
            createBot = botModule.createBot;
        }
        catch (error) {
            console.error('Failed to load bot module:', error);
            throw new Error('Bot module not available');
        }
    }
}
class BotManager {
    constructor() {
        this.tradingBot = null;
        this.eventCallbacks = {};
        // Pre-load the bot module
        loadBot().catch(console.error);
    }
    // Event emitter methods
    on(event, callback) {
        if (!this.eventCallbacks[event]) {
            this.eventCallbacks[event] = [];
        }
        this.eventCallbacks[event].push(callback);
    }
    emit(event, data) {
        if (this.eventCallbacks[event]) {
            this.eventCallbacks[event].forEach(callback => callback(data));
        }
    }
    async startBot(settings) {
        if (this.tradingBot && this.tradingBot.getStatus().isRunning) {
            return { success: false, message: 'Bot is already running' };
        }
        try {
            // Ensure bot module is loaded
            console.log('Loading bot module...');
            await loadBot();
            console.log('Bot module loaded successfully');
            console.log('Starting bot with settings:', settings);
            // Create bot configuration
            const config = {
                settings,
                pocketOptionUrl: 'https://pocketoption.com/en/cabinet/demo-quick-high-low/',
                headless: false, // Always show browser for user interaction
                userDataDir: path.join(electron_1.app.getPath('userData'), 'browser-session')
            };
            // Create new bot instance
            console.log('Creating bot instance...');
            this.tradingBot = createBot(settings, {
                headless: false,
                userDataDir: path.join(electron_1.app.getPath('userData'), 'browser-session')
            });
            console.log('Bot instance created');
            // Set up event listeners to forward events
            this.tradingBot.on('status-update', (status) => {
                this.emit('status-update', status);
            });
            this.tradingBot.on('price-update', (priceData) => {
                this.emit('price-update', priceData);
            });
            this.tradingBot.on('trade-result', (result) => {
                this.emit('trade-result', result);
            });
            this.tradingBot.on('log', (logEntry) => {
                console.log(`[BOT ${logEntry.level.toUpperCase()}] ${logEntry.message}`);
                this.emit('log', logEntry);
            });
            this.tradingBot.on('error', (error) => {
                console.error('[BOT ERROR]', error);
                this.emit('error', error);
            });
            // Start the bot
            console.log('Starting bot...');
            await this.tradingBot.start();
            console.log('Bot started successfully!');
            return { success: true, message: 'Bot started successfully' };
        }
        catch (error) {
            console.error('Failed to start bot:', error);
            return { success: false, message: `Failed to start bot: ${error}` };
        }
    }
    async stopBot() {
        if (!this.tradingBot) {
            return { success: false, message: 'No bot instance found' };
        }
        try {
            await this.tradingBot.stop();
            this.tradingBot.removeAllListeners();
            this.tradingBot = null;
            return { success: true, message: 'Bot stopped successfully' };
        }
        catch (error) {
            console.error('Failed to stop bot:', error);
            return { success: false, message: `Failed to stop bot: ${error}` };
        }
    }
    getBotStatus() {
        if (!this.tradingBot) {
            return {
                isRunning: false,
                currentPrice: 0,
                tradesCount: 0,
                winCount: 0,
                lossCount: 0,
                winRate: 0,
                totalProfit: 0,
                lastTrade: null
            };
        }
        return this.tradingBot.getStatus();
    }
    updateSettings(settings) {
        if (this.tradingBot) {
            this.tradingBot.updateSettings(settings);
            return { success: true, message: 'Settings updated successfully' };
        }
        return { success: false, message: 'No bot instance found' };
    }
}
exports.BotManager = BotManager;
//# sourceMappingURL=BotManager.js.map