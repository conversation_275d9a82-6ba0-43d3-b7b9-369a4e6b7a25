import { useState } from 'react'
import { TradingSettings } from '../../../../shared/types'

interface SettingsPanelProps {
  settings: TradingSettings
  onSave: (settings: TradingSettings) => void
  onCancel: () => void
}

export function SettingsPanel({ settings, onSave, onCancel }: SettingsPanelProps) {
  const [formData, setFormData] = useState<TradingSettings>(settings)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
  }

  const handleInputChange = (field: keyof TradingSettings, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <h2 className="text-2xl font-semibold mb-6">Trading Settings</h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Price Change Threshold (%)
            </label>
            <input
              type="number"
              step="0.01"
              min="0.01"
              max="10"
              value={formData.threshold}
              onChange={(e) => handleInputChange('threshold', parseFloat(e.target.value))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
              placeholder="0.02"
            />
            <p className="text-xs text-gray-400 mt-1">
              Minimum price change percentage to trigger a trade
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Trade Amount ($)
            </label>
            <input
              type="number"
              step="0.1"
              min="0.1"
              max="1000"
              value={formData.tradeAmount}
              onChange={(e) => handleInputChange('tradeAmount', parseFloat(e.target.value))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
              placeholder="1"
            />
            <p className="text-xs text-gray-400 mt-1">
              Amount to invest per trade
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Maximum Trades
            </label>
            <input
              type="number"
              min="1"
              max="100"
              value={formData.maxTrades}
              onChange={(e) => handleInputChange('maxTrades', parseInt(e.target.value))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
              placeholder="10"
            />
            <p className="text-xs text-gray-400 mt-1">
              Maximum number of trades per session
            </p>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="autoTrade"
              checked={formData.autoTrade}
              onChange={(e) => handleInputChange('autoTrade', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
            />
            <label htmlFor="autoTrade" className="ml-2 text-sm font-medium text-gray-300">
              Enable Auto Trading
            </label>
          </div>
        </div>

        {/* Advanced Settings */}
        <div className="border-t border-gray-700 pt-6">
          <h3 className="text-lg font-medium text-gray-200 mb-4">Advanced Settings</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Stop Loss (%)
              </label>
              <input
                type="number"
                step="0.1"
                min="0"
                max="50"
                value={formData.stopLoss || ''}
                onChange={(e) => handleInputChange('stopLoss', e.target.value ? parseFloat(e.target.value) : undefined)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                placeholder="Optional"
              />
              <p className="text-xs text-gray-400 mt-1">
                Stop trading if total loss exceeds this percentage
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Take Profit (%)
              </label>
              <input
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={formData.takeProfit || ''}
                onChange={(e) => handleInputChange('takeProfit', e.target.value ? parseFloat(e.target.value) : undefined)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                placeholder="Optional"
              />
              <p className="text-xs text-gray-400 mt-1">
                Stop trading if total profit exceeds this percentage
              </p>
            </div>
          </div>
        </div>

        {/* Warning Notice */}
        <div className="bg-yellow-900 border border-yellow-700 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-400">
                Trading Risk Warning
              </h3>
              <div className="mt-2 text-sm text-yellow-300">
                <p>
                  Trading involves significant risk. Only trade with money you can afford to lose. 
                  This bot is for educational purposes and past performance does not guarantee future results.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-700">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Save Settings
          </button>
        </div>
      </form>
    </div>
  )
}
