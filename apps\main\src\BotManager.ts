import * as path from 'path'
import { app } from 'electron'
import { TradingSettings, BotConfig } from '../../../shared/types'

// Import the bot dynamically to avoid compilation issues
let TradingBot: any = null
let createBot: any = null

// Dynamically import the bot
async function loadBot() {
	if (!TradingBot) {
		try {
			console.log('Attempting to load bot module from: ../../bot/dist/apps/bot/src/index.js')
			const botModule = await import('../../bot/dist/apps/bot/src/index.js')
			console.log('Bot module imported:', Object.keys(botModule))
			TradingBot = botModule.TradingBot
			createBot = botModule.createBot
			console.log('TradingBot:', typeof TradingBot)
			console.log('createBot:', typeof createBot)

			if (!TradingBot || !createBot) {
				throw new Error('TradingBot or createBot not found in module')
			}
		} catch (error) {
			console.error('Failed to load bot module:', error)
			const errorMessage = error instanceof Error ? error.message : String(error)
			console.error('Error details:', errorMessage)
			throw new Error(`Bot module not available: ${errorMessage}`)
		}
	}
}

export class BotManager {
	private tradingBot: any = null
	private eventCallbacks: { [key: string]: Function[] } = {}

	constructor() {
		// Pre-load the bot module
		loadBot().catch(console.error)
	}

	// Event emitter methods
	on(event: string, callback: Function) {
		if (!this.eventCallbacks[event]) {
			this.eventCallbacks[event] = []
		}
		this.eventCallbacks[event].push(callback)
	}

	emit(event: string, data: any) {
		if (this.eventCallbacks[event]) {
			this.eventCallbacks[event].forEach(callback => callback(data))
		}
	}

	async startBot(settings: TradingSettings): Promise<{ success: boolean; message: string }> {
		if (this.tradingBot && this.tradingBot.getStatus().isRunning) {
			return { success: false, message: 'Bot is already running' }
		}

		try {
			// Ensure bot module is loaded
			console.log('Loading bot module...')
			await loadBot()
			console.log('Bot module loaded successfully')

			console.log('Starting bot with settings:', settings)

			// Create bot configuration
			const config: BotConfig = {
				settings,
				pocketOptionUrl: 'https://pocketoption.com/en/cabinet/demo-quick-high-low/',
				headless: false, // Always show browser for user interaction
				userDataDir: path.join(app.getPath('userData'), 'browser-session')
			}

			// Create new bot instance
			console.log('Creating bot instance...')
			this.tradingBot = createBot(settings, {
				headless: false,
				userDataDir: path.join(app.getPath('userData'), 'browser-session')
			})
			console.log('Bot instance created')

			// Set up event listeners to forward events
			this.tradingBot.on('status-update', (status: any) => {
				this.emit('status-update', status)
			})

			this.tradingBot.on('price-update', (priceData: any) => {
				this.emit('price-update', priceData)
			})

			this.tradingBot.on('trade-result', (result: any) => {
				this.emit('trade-result', result)
			})

			this.tradingBot.on('log', (logEntry: any) => {
				console.log(`[BOT ${logEntry.level.toUpperCase()}] ${logEntry.message}`)
				this.emit('log', logEntry)
			})

			this.tradingBot.on('error', (error: any) => {
				console.error('[BOT ERROR]', error)
				this.emit('error', error)
			})

			// Start the bot
			console.log('Starting bot...')
			await this.tradingBot.start()
			console.log('Bot started successfully!')

			return { success: true, message: 'Bot started successfully' }
		} catch (error) {
			console.error('Failed to start bot:', error)
			return { success: false, message: `Failed to start bot: ${error}` }
		}
	}

	async stopBot(): Promise<{ success: boolean; message: string }> {
		if (!this.tradingBot) {
			return { success: false, message: 'No bot instance found' }
		}

		try {
			await this.tradingBot.stop()
			this.tradingBot.removeAllListeners()
			this.tradingBot = null

			return { success: true, message: 'Bot stopped successfully' }
		} catch (error) {
			console.error('Failed to stop bot:', error)
			return { success: false, message: `Failed to stop bot: ${error}` }
		}
	}

	getBotStatus() {
		if (!this.tradingBot) {
			return {
				isRunning: false,
				currentPrice: 0,
				tradesCount: 0,
				winCount: 0,
				lossCount: 0,
				winRate: 0,
				totalProfit: 0,
				lastTrade: null
			}
		}

		return this.tradingBot.getStatus()
	}

	updateSettings(settings: TradingSettings): { success: boolean; message: string } {
		if (this.tradingBot) {
			this.tradingBot.updateSettings(settings)
			return { success: true, message: 'Settings updated successfully' }
		}
		return { success: false, message: 'No bot instance found' }
	}
}
