import { app, BrowserWindow, ipcMain, dialog } from 'electron'
import * as path from 'path'
import { TradingSettings } from '../../../shared/types'
import { BotManager } from './BotManager'
import * as fs from 'fs'

// Simple settings manager
class SettingsManager {
	private settingsPath: string
	private defaultSettings = {
		windowBounds: { width: 1200, height: 800 },
		tradingSettings: {
			threshold: 0.02,
			tradeAmount: 1,
			autoTrade: false,
			maxTrades: 10
		}
	}

	constructor() {
		this.settingsPath = path.join(app.getPath('userData'), 'settings.json')
	}

	get(key: string) {
		try {
			const settings = JSON.parse(fs.readFileSync(this.settingsPath, 'utf8'))
			return settings[key] || this.defaultSettings[key as keyof typeof this.defaultSettings]
		} catch {
			return this.defaultSettings[key as keyof typeof this.defaultSettings]
		}
	}

	set(key: string, value: any) {
		try {
			let settings: any = {}
			try {
				settings = JSON.parse(fs.readFileSync(this.settingsPath, 'utf8'))
			} catch {
				// File doesn't exist or is invalid, start with empty object
			}
			settings[key] = value
			fs.writeFileSync(this.settingsPath, JSON.stringify(settings, null, 2))
		} catch (error) {
			console.error('Failed to save settings:', error)
		}
	}
}

const settings = new SettingsManager()

let mainWindow: BrowserWindow | null = null
let botManager: BotManager | null = null

const createWindow = (): void => {
	// Get stored window bounds or use defaults
	const windowBounds = settings.get('windowBounds')

	// Create the browser window
	mainWindow = new BrowserWindow({
		width: windowBounds.width,
		height: windowBounds.height,
		webPreferences: {
			nodeIntegration: false,
			contextIsolation: true,
			preload: path.join(__dirname, 'preload.js')
		},
		icon: path.join(__dirname, '../assets/icon.png'), // We'll add this later
		title: 'Pocket Option Trading Bot',
		minWidth: 800,
		minHeight: 600,
		show: false // Don't show until ready
	})

	// Load the renderer app
	const isDev = !app.isPackaged
	if (isDev) {
		mainWindow.loadURL('http://localhost:5173')
		mainWindow.webContents.openDevTools()
	} else {
		mainWindow.loadFile(path.join(__dirname, '../../renderer/dist/index.html'))
	}

	// Show window when ready
	mainWindow.once('ready-to-show', () => {
		mainWindow?.show()
	})

	// Save window bounds on resize/move
	mainWindow.on('resize', () => {
		if (mainWindow) {
			settings.set('windowBounds', mainWindow.getBounds())
		}
	})

	mainWindow.on('move', () => {
		if (mainWindow) {
			settings.set('windowBounds', mainWindow.getBounds())
		}
	})

	// Handle window closed
	mainWindow.on('closed', () => {
		mainWindow = null
	})
}

// App event handlers
app.whenReady().then(() => {
	createWindow()

	app.on('activate', () => {
		if (BrowserWindow.getAllWindows().length === 0) {
			createWindow()
		}
	})
})

app.on('window-all-closed', () => {
	if (process.platform !== 'darwin') {
		app.quit()
	}
})

// IPC handlers for communication with renderer
ipcMain.handle('get-settings', () => {
	return settings.get('tradingSettings')
})

ipcMain.handle('save-settings', (_, tradingSettings) => {
	settings.set('tradingSettings', tradingSettings)
	return true
})

ipcMain.handle('show-error-dialog', (_, title: string, content: string) => {
	if (mainWindow) {
		dialog.showErrorBox(title, content)
	}
})

ipcMain.handle('show-info-dialog', async (_, title: string, content: string) => {
	if (mainWindow) {
		const result = await dialog.showMessageBox(mainWindow, {
			type: 'info',
			title,
			message: content,
			buttons: ['OK']
		})
		return result
	}
})

// Initialize bot manager
app.whenReady().then(() => {
	botManager = new BotManager()
})

// Bot control IPC handlers
ipcMain.handle('start-bot', async (_, settings: TradingSettings) => {
	if (!botManager) {
		return { success: false, message: 'Bot manager not initialized' }
	}
	return await botManager.startBot(settings)
})

ipcMain.handle('stop-bot', async () => {
	if (!botManager) {
		return { success: false, message: 'Bot manager not initialized' }
	}
	return await botManager.stopBot()
})

ipcMain.handle('get-bot-status', () => {
	if (!botManager) {
		return {
			isRunning: false,
			currentPrice: 0,
			tradesCount: 0,
			winCount: 0,
			lossCount: 0,
			winRate: 0,
			totalProfit: 0,
			lastTrade: null
		}
	}
	return botManager.getBotStatus()
})

ipcMain.handle('update-bot-settings', (_, settings: TradingSettings) => {
	if (!botManager) {
		return { success: false, message: 'Bot manager not initialized' }
	}
	return botManager.updateSettings(settings)
})
