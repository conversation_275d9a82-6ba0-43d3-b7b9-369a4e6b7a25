import { TradingSettings } from '../../../shared/types';
export declare class BotManager {
    private botProcess;
    private isRunning;
    startBot(settings: TradingSettings): Promise<{
        success: boolean;
        message: string;
    }>;
    stopBot(): Promise<{
        success: boolean;
        message: string;
    }>;
    getBotStatus(): {
        isRunning: boolean;
        currentPrice: number;
        tradesCount: number;
        winCount: number;
        lossCount: number;
        winRate: number;
        totalProfit: number;
        lastTrade: null;
    };
    updateSettings(settings: TradingSettings): {
        success: boolean;
        message: string;
    };
}
//# sourceMappingURL=BotManager.d.ts.map