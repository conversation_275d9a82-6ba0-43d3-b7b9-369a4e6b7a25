import type { BotStatus, PriceData, TradeResult } from '../../../../shared/types'

interface TradingDashboardProps {
	botStatus: BotStatus
	currentPrice: PriceData | null
	recentTrades: TradeResult[]
	onStartBot: () => void
	onStopBot: () => void
}

export function TradingDashboard({
	botStatus,
	currentPrice,
	recentTrades,
	onStartBot,
	onStopBot
}: TradingDashboardProps) {
	const formatPrice = (price: number) => price.toFixed(5)
	const formatPercent = (percent: number) => `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`
	const formatProfit = (profit: number) => `${profit >= 0 ? '+' : ''}$${profit.toFixed(2)}`
	const formatUptime = (uptime?: number) => {
		if (!uptime) return '00:00:00'
		const hours = Math.floor(uptime / 3600000)
		const minutes = Math.floor((uptime % 3600000) / 60000)
		const seconds = Math.floor((uptime % 60000) / 1000)
		return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds
			.toString()
			.padStart(2, '0')}`
	}

	const getTrendColor = (trend?: string) => {
		switch (trend) {
			case 'up':
				return 'text-green-400'
			case 'down':
				return 'text-red-400'
			default:
				return 'text-gray-400'
		}
	}

	const getTrendIcon = (trend?: string) => {
		switch (trend) {
			case 'up':
				return '↗'
			case 'down':
				return '↘'
			default:
				return '→'
		}
	}

	return (
		<div className="space-y-6">
			{/* Control Panel */}
			<div className="bg-gray-800 rounded-lg p-6">
				<div className="flex items-center justify-between mb-4">
					<h2 className="text-xl font-semibold">Bot Control</h2>
					<div
						className={`px-3 py-1 rounded-full text-sm font-medium ${
							botStatus.isRunning ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
						}`}
					>
						{botStatus.isRunning ? 'Running' : 'Stopped'}
					</div>
				</div>

				<div className="flex gap-4">
					<button
						onClick={onStartBot}
						disabled={botStatus.isRunning}
						className={`px-6 py-3 rounded-lg font-medium transition-colors ${
							botStatus.isRunning
								? 'bg-gray-600 text-gray-400 cursor-not-allowed'
								: 'bg-green-600 hover:bg-green-700 text-white'
						}`}
					>
						Start Bot
					</button>

					<button
						onClick={onStopBot}
						disabled={!botStatus.isRunning}
						className={`px-6 py-3 rounded-lg font-medium transition-colors ${
							!botStatus.isRunning
								? 'bg-gray-600 text-gray-400 cursor-not-allowed'
								: 'bg-red-600 hover:bg-red-700 text-white'
						}`}
					>
						Stop Bot
					</button>
				</div>
			</div>

			{/* Price Display */}
			<div className="bg-gray-800 rounded-lg p-6">
				<h2 className="text-xl font-semibold mb-4">Current Price</h2>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="text-center">
						<div className="text-3xl font-bold text-blue-400">
							{currentPrice ? formatPrice(currentPrice.current) : formatPrice(botStatus.currentPrice)}
						</div>
						<div className="text-gray-400">Current Price</div>
					</div>

					{currentPrice && (
						<>
							<div className="text-center">
								<div className={`text-2xl font-bold ${getTrendColor(currentPrice.trend)}`}>
									{getTrendIcon(currentPrice.trend)} {formatPercent(currentPrice.changePercent)}
								</div>
								<div className="text-gray-400">Change</div>
							</div>

							<div className="text-center">
								<div className={`text-2xl font-bold ${getTrendColor(currentPrice.trend)}`}>
									{formatPrice(Math.abs(currentPrice.change))}
								</div>
								<div className="text-gray-400">Price Change</div>
							</div>
						</>
					)}
				</div>
			</div>

			{/* Statistics */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<div className="bg-gray-800 rounded-lg p-4">
					<div className="text-2xl font-bold text-blue-400">{botStatus.tradesCount}</div>
					<div className="text-gray-400">Total Trades</div>
				</div>

				<div className="bg-gray-800 rounded-lg p-4">
					<div className="text-2xl font-bold text-green-400">{botStatus.winRate.toFixed(1)}%</div>
					<div className="text-gray-400">Win Rate</div>
				</div>

				<div className="bg-gray-800 rounded-lg p-4">
					<div className={`text-2xl font-bold ${botStatus.totalProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
						{formatProfit(botStatus.totalProfit)}
					</div>
					<div className="text-gray-400">Total Profit</div>
				</div>

				<div className="bg-gray-800 rounded-lg p-4">
					<div className="text-2xl font-bold text-purple-400">{formatUptime(botStatus.uptime)}</div>
					<div className="text-gray-400">Uptime</div>
				</div>
			</div>

			{/* Recent Trades */}
			<div className="bg-gray-800 rounded-lg p-6">
				<h2 className="text-xl font-semibold mb-4">Recent Trades</h2>
				{recentTrades.length === 0 ? (
					<div className="text-gray-400 text-center py-8">No trades yet</div>
				) : (
					<div className="overflow-x-auto">
						<table className="w-full text-sm">
							<thead>
								<tr className="border-b border-gray-700">
									<th className="text-left py-2">Time</th>
									<th className="text-left py-2">Direction</th>
									<th className="text-left py-2">Entry Price</th>
									<th className="text-left py-2">Exit Price</th>
									<th className="text-left py-2">Result</th>
									<th className="text-left py-2">Profit</th>
								</tr>
							</thead>
							<tbody>
								{recentTrades.map(trade => (
									<tr key={trade.id} className="border-b border-gray-700">
										<td className="py-2">{new Date(trade.timestamp).toLocaleTimeString()}</td>
										<td className="py-2">
											<span
												className={`px-2 py-1 rounded text-xs ${
													trade.direction === 'high' ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
												}`}
											>
												{trade.direction.toUpperCase()}
											</span>
										</td>
										<td className="py-2">{formatPrice(trade.entryPrice)}</td>
										<td className="py-2">{trade.exitPrice ? formatPrice(trade.exitPrice) : '-'}</td>
										<td className="py-2">
											<span
												className={`px-2 py-1 rounded text-xs ${
													trade.result === 'win'
														? 'bg-green-900 text-green-300'
														: trade.result === 'loss'
														? 'bg-red-900 text-red-300'
														: 'bg-yellow-900 text-yellow-300'
												}`}
											>
												{trade.result.toUpperCase()}
											</span>
										</td>
										<td className={`py-2 ${trade.profit && trade.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
											{trade.profit ? formatProfit(trade.profit) : '-'}
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				)}
			</div>
		</div>
	)
}
