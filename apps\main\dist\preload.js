"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Define the API that will be exposed to the renderer process
const electronAPI = {
    // Settings management
    getSettings: () => electron_1.ipcRenderer.invoke('get-settings'),
    saveSettings: (settings) => electron_1.ipcRenderer.invoke('save-settings', settings),
    // Dialog utilities
    showErrorDialog: (title, content) => electron_1.ipcRenderer.invoke('show-error-dialog', title, content),
    showInfoDialog: (title, content) => electron_1.ipcRenderer.invoke('show-info-dialog', title, content),
    // Bot control
    startBot: (settings) => electron_1.ipcRenderer.invoke('start-bot', settings),
    stopBot: () => electron_1.ipcRenderer.invoke('stop-bot'),
    getBotStatus: () => electron_1.ipcRenderer.invoke('get-bot-status'),
    updateBotSettings: (settings) => electron_1.ipcRenderer.invoke('update-bot-settings', settings),
    // Bot status updates (for real-time data)
    onBotStatusUpdate: (callback) => {
        electron_1.ipcRenderer.on('bot-status-update', (_, status) => callback(status));
    },
    onPriceUpdate: (callback) => {
        electron_1.ipcRenderer.on('price-update', (_, price) => callback(price));
    },
    onTradeResult: (callback) => {
        electron_1.ipcRenderer.on('trade-result', (_, result) => callback(result));
    },
    // Remove listeners
    removeAllListeners: (channel) => {
        electron_1.ipcRenderer.removeAllListeners(channel);
    }
};
// Expose the API to the renderer process
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
//# sourceMappingURL=preload.js.map