declare const electronAPI: {
    getSettings: () => Promise<any>;
    saveSettings: (settings: any) => Promise<any>;
    showErrorDialog: (title: string, content: string) => Promise<any>;
    showInfoDialog: (title: string, content: string) => Promise<any>;
    startBot: (settings: any) => Promise<any>;
    stopBot: () => Promise<any>;
    getBotStatus: () => Promise<any>;
    updateBotSettings: (settings: any) => Promise<any>;
    onBotStatusUpdate: (callback: (status: any) => void) => void;
    onPriceUpdate: (callback: (price: number) => void) => void;
    onTradeResult: (callback: (result: any) => void) => void;
    removeAllListeners: (channel: string) => void;
};
export type ElectronAPI = typeof electronAPI;
export {};
//# sourceMappingURL=preload.d.ts.map