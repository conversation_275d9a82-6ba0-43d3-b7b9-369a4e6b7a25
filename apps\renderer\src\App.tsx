import { useState, useEffect } from 'react'
import { TradingDashboard } from './components/TradingDashboard'
import { SettingsPanel } from './components/SettingsPanel'
import { TradingSettings, BotStatus, PriceData, TradeResult } from '../../../shared/types'

// Extend Window interface for TypeScript
declare global {
	interface Window {
		electronAPI: {
			getSettings: () => Promise<TradingSettings>
			saveSettings: (settings: TradingSettings) => Promise<boolean>
			startBot: (settings: TradingSettings) => Promise<{ success: boolean; message: string }>
			stopBot: () => Promise<{ success: boolean; message: string }>
			getBotStatus: () => Promise<BotStatus>
			updateBotSettings: (settings: TradingSettings) => Promise<{ success: boolean; message: string }>
			onBotStatusUpdate: (callback: (status: BotStatus) => void) => void
			onPriceUpdate: (callback: (price: PriceData) => void) => void
			onTradeResult: (callback: (result: TradeResult) => void) => void
			showErrorDialog: (title: string, content: string) => void
			showInfoDialog: (title: string, content: string) => void
			removeAllListeners: (channel: string) => void
		}
	}
}

function App() {
	const [settings, setSettings] = useState<TradingSettings>({
		threshold: 0.02,
		tradeAmount: 1,
		autoTrade: false,
		maxTrades: 10
	})

	const [botStatus, setBotStatus] = useState<BotStatus>({
		isRunning: false,
		currentPrice: 0,
		tradesCount: 0,
		winCount: 0,
		lossCount: 0,
		winRate: 0,
		totalProfit: 0,
		lastTrade: null
	})

	const [currentPrice, setCurrentPrice] = useState<PriceData | null>(null)
	const [recentTrades, setRecentTrades] = useState<TradeResult[]>([])
	const [showSettings, setShowSettings] = useState(false)

	// Load settings on component mount
	useEffect(() => {
		const loadSettings = async () => {
			try {
				const savedSettings = await window.electronAPI.getSettings()
				setSettings(savedSettings)
			} catch (error) {
				console.error('Failed to load settings:', error)
			}
		}

		loadSettings()
	}, [])

	// Set up event listeners
	useEffect(() => {
		// Bot status updates
		window.electronAPI.onBotStatusUpdate((status: BotStatus) => {
			setBotStatus(status)
		})

		// Price updates
		window.electronAPI.onPriceUpdate((priceData: PriceData) => {
			setCurrentPrice(priceData)
		})

		// Trade results
		window.electronAPI.onTradeResult((result: TradeResult) => {
			setRecentTrades(prev => [result, ...prev.slice(0, 9)]) // Keep last 10 trades
		})

		// Cleanup listeners on unmount
		return () => {
			window.electronAPI.removeAllListeners('bot-status-update')
			window.electronAPI.removeAllListeners('price-update')
			window.electronAPI.removeAllListeners('trade-result')
		}
	}, [])

	const handleStartBot = async () => {
		try {
			const result = await window.electronAPI.startBot(settings)
			if (result.success) {
				window.electronAPI.showInfoDialog('Success', result.message)
			} else {
				window.electronAPI.showErrorDialog('Error', result.message)
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to start bot: ${error}`)
		}
	}

	const handleStopBot = async () => {
		try {
			const result = await window.electronAPI.stopBot()
			if (result.success) {
				window.electronAPI.showInfoDialog('Success', result.message)
			} else {
				window.electronAPI.showErrorDialog('Error', result.message)
			}
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to stop bot: ${error}`)
		}
	}

	const handleSaveSettings = async (newSettings: TradingSettings) => {
		try {
			await window.electronAPI.saveSettings(newSettings)
			setSettings(newSettings)

			// Update bot settings if running
			if (botStatus.isRunning) {
				await window.electronAPI.updateBotSettings(newSettings)
			}

			setShowSettings(false)
			window.electronAPI.showInfoDialog('Success', 'Settings saved successfully')
		} catch (error) {
			window.electronAPI.showErrorDialog('Error', `Failed to save settings: ${error}`)
		}
	}

	return (
		<div className="min-h-screen bg-gray-900 text-white">
			<div className="container mx-auto px-4 py-6">
				<header className="flex justify-between items-center mb-8">
					<h1 className="text-3xl font-bold text-blue-400">Pocket Option Trading Bot</h1>
					<button
						onClick={() => setShowSettings(!showSettings)}
						className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
					>
						Settings
					</button>
				</header>

				{showSettings ? (
					<SettingsPanel settings={settings} onSave={handleSaveSettings} onCancel={() => setShowSettings(false)} />
				) : (
					<TradingDashboard
						botStatus={botStatus}
						currentPrice={currentPrice}
						recentTrades={recentTrades}
						onStartBot={handleStartBot}
						onStopBot={handleStopBot}
					/>
				)}
			</div>
		</div>
	)
}

export default App
